#!/usr/bin/env python3
"""
参数日志功能演示脚本
展示新的参数修改日志记录功能 - 记录所有参数
"""

import sys
import os
import time
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from parameter_logger import get_parameter_logger, setup_parameter_logging


def demo_parameter_logging():
    """演示参数日志记录功能"""
    print("=" * 80)
    print("参数修改日志记录系统演示")
    print("=" * 80)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 创建临时目录用于演示
    import tempfile
    temp_dir = tempfile.mkdtemp(prefix="param_demo_")

    # 设置日志系统（记录所有参数）
    setup_parameter_logging(
        log_file=os.path.join(temp_dir, "demo_parameter_changes.log")
    )

    param_logger = get_parameter_logger()

    print("✅ 系统特性:")
    print("   - 记录所有参数的修改，不进行重要性过滤")
    print("   - 记录修改前后的值和变化量")
    print("   - 追踪修改来源（Web API、自动设置、UDP等）")
    print("   - 记录客户端信息（设备ID、IP地址）")
    print("   - 同时保存到日志文件和SQLite数据库")
    print()

    # 演示不同类型的参数修改
    print("📝 演示参数修改记录:")
    print("-" * 50)

    # 1. 关键参数修改
    print("1. 关键参数修改 (max_rpm):")
    param_logger.log_parameter_change(
        param_name="max_rpm",
        old_value=1500.0,
        new_value=1800.0,
        source="web_api(*************)",
        client_info={
            "device_id": "演示车001",
            "client_ip": "*************"
        },
        success=True
    )

    # 2. 控制参数修改
    print("2. 控制参数修改 (spd_kp):")
    param_logger.log_parameter_change(
        param_name="spd_kp",
        old_value=0.5,
        new_value=0.8,
        source="auto_setup(*************)",
        client_info={
            "device_id": "演示车001",
            "client_ip": "*************"
        },
        success=True
    )

    # 3. 调试参数修改
    print("3. 调试参数修改 (sleepTm):")
    param_logger.log_parameter_change(
        param_name="sleepTm",
        old_value=5.0,
        new_value=10.0,
        source="udp_handler",
        success=True
    )

    # 4. 系统参数修改
    print("4. 系统参数修改 (pwr_sta):")
    param_logger.log_parameter_change(
        param_name="pwr_sta",
        old_value=0.0,
        new_value=1.0,
        source="system_init",
        success=True
    )

    # 5. 未知参数修改
    print("5. 未知参数修改 (custom_param):")
    param_logger.log_parameter_change(
        param_name="custom_param",
        old_value=None,  # 首次设置
        new_value=42.0,
        source="manual_test",
        success=True
    )

    # 6. 失败的参数修改
    print("6. 失败的参数修改 (max_current):")
    param_logger.log_parameter_change(
        param_name="max_current",
        old_value=50.0,
        new_value=80.0,
        source="web_api(*************)",
        client_info={
            "device_id": "演示车002",
            "client_ip": "*************"
        },
        success=False  # 修改失败
    )

    print()

    # 演示客户端登录记录
    print("🔐 演示客户端登录记录:")
    print("-" * 50)

    param_logger.log_client_login(
        device_id="演示车001",
        client_ip="*************",
        web_port=7002,
        server_type="custom"
    )

    param_logger.log_client_login(
        device_id="演示车002",
        client_ip="*************",
        web_port=7002,
        server_type="default"
    )

    print()

    # 等待一下确保数据写入
    time.sleep(1)

    # 展示统计信息
    print("📊 参数统计信息:")
    print("-" * 50)

    stats = param_logger.get_parameter_stats()
    print(f"总参数数量: {stats.get('total_parameters', 0)}")

    print("按重要性分类:")
    for importance, count in stats.get('by_importance', {}).items():
        print(f"  {importance}: {count} 个参数")

    if 'recent_changes' in stats:
        recent = stats['recent_changes']
        print(f"\n最近修改统计:")
        print(f"  总修改次数: {recent.get('total_changes', 0)}")
        print(f"  涉及参数数: {recent.get('unique_parameters', 0)}")
        print(f"  涉及设备数: {recent.get('unique_devices', 0)}")

    print()

    # 展示历史查询
    print("📜 参数修改历史查询:")
    print("-" * 50)

    # 查询所有修改历史
    history = param_logger.get_parameter_history(limit=10)
    if history:
        print("最近的参数修改:")
        for i, record in enumerate(history[:5], 1):
            status = "✅" if record['success'] else "❌"
            change_info = f"{record['old_value']} → {record['new_value']}" if record['old_value'] is not None else f"设置为 {record['new_value']}"
            print(f"  {i}. {status} {record['parameter_name']}: {change_info}")
            print(f"     来源: {record['source']} | 时间: {record['timestamp'][:19]}")
            if record['device_id']:
                print(f"     设备: {record['device_id']} | IP: {record['client_ip']}")
    else:
        print("  暂无历史记录")

    print()

    # 查询登录历史
    print("🔍 客户端登录历史:")
    print("-" * 50)

    login_history = param_logger.get_login_history(limit=5)
    if login_history:
        for i, record in enumerate(login_history, 1):
            print(f"  {i}. 设备: {record['device_id']} | IP: {record['client_ip']}")
            print(f"     端口: {record['web_port']} | 服务器: {record['server_type']}")
            print(f"     时间: {record['timestamp'][:19]}")
    else:
        print("  暂无登录记录")

    print()
    print("=" * 80)
    print("演示完成！")
    print("=" * 80)
    print("📁 生成的文件:")
    print(f"   - 日志文件: {param_logger.log_file}")
    print(f"   - 历史数据库: {param_logger.history_db}")
    print()
    print("💡 特点总结:")
    print("   ✅ 记录所有参数修改，无重要性过滤")
    print("   ✅ 详细的修改信息（前值、后值、变化量）")
    print("   ✅ 完整的来源追踪")
    print("   ✅ 客户端信息记录")
    print("   ✅ 成功/失败状态记录")
    print("   ✅ 历史查询和统计功能")
    print("   ✅ 双重存储（日志文件 + 数据库）")


if __name__ == "__main__":
    try:
        demo_parameter_logging()
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
