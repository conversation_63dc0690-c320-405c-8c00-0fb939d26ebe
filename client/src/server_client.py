#!/usr/bin/env python3
"""
服务器连接模块
TCP客户端连接服务器，发送设备信息和心跳
"""

import socket
import json
import asyncio
import logging
import time
import os
from datetime import datetime
from typing import Optional

# 导入参数日志记录器
from parameter_logger import get_parameter_logger


class ServerClient:
    """双服务器连接客户端"""

    def __init__(self, config_file: str = "config.json"):
        """初始化服务器客户端"""
        self.config = self.load_config(config_file)
        self.logger = logging.getLogger(__name__)

        # 双服务器配置
        self.servers = [
            {
                'name': 'default',
                'host': '**************',
                'port': 7777,
                'socket': None,
                'connected': False,
                'last_heartbeat': None
            },
            {
                'name': 'custom',
                'host': self.config['server']['host'],
                'port': self.config['server']['port'],
                'socket': None,
                'connected': False,
                'last_heartbeat': None
            }
        ]

        # 设备配置
        self.device_id = self.config['device']['id']
        self.web_port = self.config['device']['web_port']

        # 连接状态
        self.running = False

        # 心跳配置
        self.heartbeat_interval = 30  # 30秒心跳间隔
        self.reconnect_interval = 5   # 5秒重连间隔

    def load_config(self, config_file: str) -> dict:
        """加载配置（优先使用环境变量）"""
        # 优先使用环境变量，设置默认值
        return {
            'server': {
                'host': os.getenv('SERVER_HOST', '*************'),
                'port': int(os.getenv('SERVER_PORT', '8888'))
            },
            'device': {
                'id': os.getenv('DEVICE_ID', '太阳能通信车001'),
                'web_port': int(os.getenv('WEB_PORT', '7002'))
            }
        }

    def get_local_ip(self) -> str:
        """获取本地IP地址（优先选择192.168.191.x网段）"""
        import os
        host_ip = os.getenv('HOST_IP')
        if host_ip:
            self.logger.info(f"使用配置的主机IP: {host_ip}")
            return host_ip

        try:
            import netifaces

            # 获取所有网络接口
            interfaces = netifaces.interfaces()
            candidate_ips = []

            for interface in interfaces:
                try:
                    # 获取接口的地址信息
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr_info in addrs[netifaces.AF_INET]:
                            ip = addr_info.get('addr')
                            if ip and not ip.startswith('127.'):  # 排除回环地址
                                candidate_ips.append(ip)
                except Exception:
                    continue

            # 优先选择192.168.191.x网段的IP
            for ip in candidate_ips:
                if ip.startswith('192.168.191.'):
                    self.logger.info(f"选择优先IP地址: {ip} (192.168.191.x网段)")
                    return ip

            # 其次选择192.168.x.x网段的IP
            for ip in candidate_ips:
                if ip.startswith('192.168.'):
                    self.logger.info(f"选择IP地址: {ip} (192.168.x.x网段)")
                    return ip

            # 最后选择其他私有网段的IP
            for ip in candidate_ips:
                if ip.startswith('10.') or ip.startswith('172.'):
                    self.logger.info(f"选择IP地址: {ip} (其他私有网段)")
                    return ip

            # 如果都没有，选择第一个可用的IP
            if candidate_ips:
                ip = candidate_ips[0]
                self.logger.info(f"选择IP地址: {ip} (第一个可用)")
                return ip

        except ImportError:
            self.logger.warning("netifaces模块未安装，使用备用方法获取IP")
        except Exception as e:
            self.logger.warning(f"使用netifaces获取IP失败: {e}，使用备用方法")

        # 备用方法：尝试连接到服务器地址来获取本地IP
        try:
            server_host = self.servers[0]['host'] if self.servers else '*************'
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect((server_host, 80))
                local_ip = s.getsockname()[0]
                self.logger.info(f"使用备用方法获取IP地址: {local_ip}")
                return local_ip
        except Exception:
            pass

        # 最后的备用方法：连接到外部地址
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                self.logger.info(f"使用外部连接获取IP地址: {local_ip}")
                return local_ip
        except Exception:
            # 如果都失败，返回localhost
            self.logger.error("所有获取IP地址的方法都失败，返回127.0.0.1")
            return "127.0.0.1"

    async def connect_to_server(self, server_info: dict) -> bool:
        """连接到指定服务器"""
        try:
            # 创建TCP socket
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.settimeout(10)  # 10秒连接超时

            # 连接服务器
            server_socket.connect((server_info['host'], server_info['port']))
            self.logger.info(f"连接{server_info['name']}服务器成功: {server_info['host']}:{server_info['port']}")

            # 发送设备信息
            client_ip = self.get_local_ip()
            device_info = {
                "device_id": self.device_id,
                "ip": client_ip,
                "web_port": self.web_port,
                "timestamp": datetime.now().isoformat(),
                "server_type": server_info['name']
            }

            message = json.dumps(device_info).encode('utf-8')
            server_socket.send(message)
            self.logger.info(f"向{server_info['name']}服务器发送设备信息: {device_info}")

            # 接收服务器响应
            response_data = server_socket.recv(1024)
            response = json.loads(response_data.decode('utf-8'))

            if response.get('status') == 'connected':
                server_info['socket'] = server_socket
                server_info['connected'] = True
                server_info['last_heartbeat'] = datetime.now()
                self.logger.info(f"{server_info['name']}服务器响应: {response.get('message', '连接成功')}")

                # 记录客户端登录到参数日志
                try:
                    param_logger = get_parameter_logger()
                    param_logger.log_client_login(
                        device_id=self.device_id,
                        client_ip=client_ip,
                        web_port=self.web_port,
                        server_type=server_info['name']
                    )
                except Exception as log_error:
                    self.logger.warning(f"记录客户端登录日志失败: {log_error}")

                return True
            else:
                self.logger.error(f"{server_info['name']}服务器拒绝连接: {response}")
                server_socket.close()
                return False

        except Exception as e:
            self.logger.error(f"连接{server_info['name']}服务器失败: {e}")
            if 'server_socket' in locals():
                server_socket.close()
            return False

    async def send_heartbeat(self, server_info: dict) -> bool:
        """向指定服务器发送心跳"""
        try:
            if not server_info['socket'] or not server_info['connected']:
                return False

            # 发送心跳消息
            heartbeat_msg = b"heartbeat"
            server_info['socket'].send(heartbeat_msg)

            # 接收心跳响应
            server_info['socket'].settimeout(5)  # 5秒响应超时
            response = server_info['socket'].recv(1024)

            if response == b"heartbeat_ack":
                server_info['last_heartbeat'] = datetime.now()
                self.logger.debug(f"{server_info['name']}服务器心跳响应正常")
                return True
            else:
                self.logger.warning(f"{server_info['name']}服务器心跳响应异常: {response}")
                return False

        except Exception as e:
            self.logger.error(f"向{server_info['name']}服务器发送心跳失败: {e}")
            return False

    async def disconnect(self):
        """断开所有服务器连接"""
        for server in self.servers:
            if server['socket']:
                try:
                    server['socket'].close()
                except Exception:
                    pass
                server['socket'] = None
                server['connected'] = False
                self.logger.info(f"已断开{server['name']}服务器连接")
        self.logger.info("已断开所有服务器连接")

    async def heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            for server in self.servers:
                if server['connected']:
                    success = await self.send_heartbeat(server)
                    if not success:
                        self.logger.warning(f"{server['name']}服务器心跳失败，标记为断开连接")
                        server['connected'] = False

            await asyncio.sleep(self.heartbeat_interval)

    async def connection_loop(self):
        """连接维护循环"""
        while self.running:
            any_disconnected = False
            for server in self.servers:
                if not server['connected']:
                    any_disconnected = True
                    self.logger.info(f"尝试连接{server['name']}服务器...")
                    success = await self.connect_to_server(server)

                    if not success:
                        self.logger.info(f"连接{server['name']}服务器失败")
                    else:
                        self.logger.info(f"成功连接到{server['name']}服务器")

            # 如果有服务器未连接，等待一段时间后重试
            if any_disconnected:
                await asyncio.sleep(self.reconnect_interval)
            else:
                await asyncio.sleep(1)  # 检查连接状态间隔

    async def start(self):
        """启动服务器客户端"""
        self.running = True
        self.logger.info("启动服务器客户端...")

        # 启动连接循环和心跳循环
        connection_task = asyncio.create_task(self.connection_loop())
        heartbeat_task = asyncio.create_task(self.heartbeat_loop())

        try:
            await asyncio.gather(connection_task, heartbeat_task)
        except Exception as e:
            self.logger.error(f"服务器客户端运行错误: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """停止服务器客户端"""
        self.running = False
        await self.disconnect()
        self.logger.info("服务器客户端已停止")

    def get_status(self) -> dict:
        """获取连接状态"""
        servers_status = []
        for server in self.servers:
            servers_status.append({
                'name': server['name'],
                'host': server['host'],
                'port': server['port'],
                'connected': server['connected'],
                'last_heartbeat': server['last_heartbeat'].isoformat() if server['last_heartbeat'] else None
            })

        return {
            'running': self.running,
            'servers': servers_status,
            'device_id': self.device_id,
            'local_ip': self.get_local_ip() if self.running else None,
            'connected_count': sum(1 for s in self.servers if s['connected'])
        }

    def reload_config(self, new_config: dict):
        """重新加载配置"""
        try:
            # 更新设备配置
            self.device_id = new_config.get('DEVICE_ID', self.device_id)
            self.web_port = int(new_config.get('WEB_PORT', self.web_port))

            # 更新自定义服务器配置
            custom_server = next((s for s in self.servers if s['name'] == 'custom'), None)
            if custom_server:
                old_host = custom_server['host']
                old_port = custom_server['port']
                new_host = new_config.get('SERVER_HOST', old_host)
                new_port = int(new_config.get('SERVER_PORT', old_port))

                # 如果服务器配置有变化，断开重连
                if old_host != new_host or old_port != new_port:
                    self.logger.info(f"自定义服务器配置变化: {old_host}:{old_port} -> {new_host}:{new_port}")

                    # 断开旧连接
                    if custom_server['socket']:
                        custom_server['socket'].close()
                        custom_server['socket'] = None
                        custom_server['connected'] = False

                    # 更新配置
                    custom_server['host'] = new_host
                    custom_server['port'] = new_port

                    self.logger.info("自定义服务器配置已更新，将在下次连接循环中重新连接")

            self.logger.info("服务器客户端配置重载完成")

        except Exception as e:
            self.logger.error(f"重新加载服务器客户端配置失败: {e}")
            raise


async def test_server_client():
    """测试服务器客户端"""
    print("测试服务器客户端...")

    client = ServerClient()

    # 启动客户端（运行5秒后停止）
    try:
        task = asyncio.create_task(client.start())
        await asyncio.sleep(5)
        await client.stop()

    except KeyboardInterrupt:
        print("用户中断")
        await client.stop()


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    asyncio.run(test_server_client())
