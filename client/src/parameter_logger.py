#!/usr/bin/env python3
"""
参数修改日志记录器
专门用于记录参数修改的结构化日志，支持重要性分类和过滤
"""

import os
import json
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum


class ParameterImportance(Enum):
    """参数重要性级别"""
    CRITICAL = "critical"      # 核心参数，必须记录
    IMPORTANT = "important"    # 重要参数，建议记录
    DEBUG = "debug"           # 调试参数，可选记录
    SYSTEM = "system"         # 系统参数，简化记录


class ParameterClassifier:
    """参数分类器"""

    def __init__(self, config_file: str = "parameter_classification.json"):
        """初始化参数分类器"""
        self.config_file = config_file
        self.classification = self._load_classification()

    def _load_classification(self) -> Dict[str, str]:
        """加载参数分类配置"""
        default_classification = {
            # 核心控制参数
            "max_rpm": ParameterImportance.CRITICAL.value,
            "max_acc": ParameterImportance.CRITICAL.value,
            "maxTorq": ParameterImportance.CRITICAL.value,
            "feedPwr": ParameterImportance.CRITICAL.value,
            "dispPwr": ParameterImportance.CRITICAL.value,
            "VehMass": ParameterImportance.CRITICAL.value,
            "gRatio": ParameterImportance.CRITICAL.value,

            # 重要PID参数
            "spd_kp": ParameterImportance.IMPORTANT.value,
            "spd_ki": ParameterImportance.IMPORTANT.value,
            "spd_kd": ParameterImportance.IMPORTANT.value,
            "spd_il": ParameterImportance.IMPORTANT.value,
            "spd_ol": ParameterImportance.IMPORTANT.value,
            "crv_kp": ParameterImportance.IMPORTANT.value,
            "crv_ki": ParameterImportance.IMPORTANT.value,
            "crv_kd": ParameterImportance.IMPORTANT.value,
            "crv_il": ParameterImportance.IMPORTANT.value,
            "crv_ol": ParameterImportance.IMPORTANT.value,

            # 重要安全参数
            "brk_on": ParameterImportance.IMPORTANT.value,
            "brk_off": ParameterImportance.IMPORTANT.value,
            "brk_pos": ParameterImportance.IMPORTANT.value,
            "brk_rev": ParameterImportance.IMPORTANT.value,
            "prCTime": ParameterImportance.IMPORTANT.value,

            # 自主驾驶参数
            "Ospd_kp": ParameterImportance.IMPORTANT.value,
            "Ospd_ki": ParameterImportance.IMPORTANT.value,
            "Ospd_kd": ParameterImportance.IMPORTANT.value,
            "Ospd_il": ParameterImportance.IMPORTANT.value,
            "Ospd_ol": ParameterImportance.IMPORTANT.value,
            "Ocrv_kp": ParameterImportance.IMPORTANT.value,
            "Ocrv_ki": ParameterImportance.IMPORTANT.value,
            "Ocrv_kd": ParameterImportance.IMPORTANT.value,
            "Ocrv_il": ParameterImportance.IMPORTANT.value,
            "Ocrv_ol": ParameterImportance.IMPORTANT.value,

            # 调试参数
            "sleepTm": ParameterImportance.DEBUG.value,
            "wakeTm": ParameterImportance.DEBUG.value,
            "test": ParameterImportance.DEBUG.value,
            "minTorq": ParameterImportance.DEBUG.value,
            "mot_kp": ParameterImportance.DEBUG.value,
            "mot_ki": ParameterImportance.DEBUG.value,
            "mot_kd": ParameterImportance.DEBUG.value,
            "mot_il": ParameterImportance.DEBUG.value,
            "mot_ol": ParameterImportance.DEBUG.value,
            "diff_sp": ParameterImportance.DEBUG.value,

            # 系统状态参数
            "pwr_sta": ParameterImportance.SYSTEM.value,
            "high_sw": ParameterImportance.SYSTEM.value,
            "stop_sw": ParameterImportance.SYSTEM.value,
            "lightSt": ParameterImportance.SYSTEM.value,
            "pwr_btn": ParameterImportance.SYSTEM.value,

            # 物理参数
            "whl_bas": ParameterImportance.IMPORTANT.value,
            "whl_dia": ParameterImportance.IMPORTANT.value,
        }

        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    default_classification.update(loaded_config.get('classification', {}))
            else:
                # 创建默认配置文件
                self._save_classification(default_classification)
        except Exception as e:
            logging.warning(f"加载参数分类配置失败，使用默认配置: {e}")

        return default_classification

    def _save_classification(self, classification: Dict[str, str]):
        """保存参数分类配置"""
        try:
            config = {
                "classification": classification,
                "last_updated": datetime.now().isoformat(),
                "description": "参数重要性分类配置"
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存参数分类配置失败: {e}")

    def get_importance(self, param_name: str) -> ParameterImportance:
        """获取参数重要性级别"""
        importance_str = self.classification.get(param_name, ParameterImportance.DEBUG.value)
        try:
            return ParameterImportance(importance_str)
        except ValueError:
            return ParameterImportance.DEBUG

    def is_important_enough(self, param_name: str, min_level: ParameterImportance) -> bool:
        """判断参数是否足够重要需要记录"""
        param_importance = self.get_importance(param_name)

        # 定义重要性级别顺序
        importance_order = {
            ParameterImportance.CRITICAL: 4,
            ParameterImportance.IMPORTANT: 3,
            ParameterImportance.DEBUG: 2,
            ParameterImportance.SYSTEM: 1
        }

        return importance_order.get(param_importance, 0) >= importance_order.get(min_level, 0)


class ParameterLogger:
    """参数修改日志记录器"""

    def __init__(self, log_file: str = "logs/parameter_changes.log",
                 min_importance: ParameterImportance = ParameterImportance.SYSTEM,
                 history_db: str = "logs/parameter_history.db"):
        """初始化参数日志记录器"""
        self.log_file = log_file
        self.history_db = history_db
        self.min_importance = min_importance
        self.classifier = ParameterClassifier()

        # 创建日志目录
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        db_dir = os.path.dirname(history_db)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)

        # 初始化历史数据库
        self._init_history_db()

        # 设置日志记录器
        self.logger = logging.getLogger(f"parameter_logger_{id(self)}")
        self.logger.setLevel(logging.INFO)

        # 避免重复添加处理器
        if not self.logger.handlers:
            # 创建文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)

            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s - PARAM_CHANGE - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)

            self.logger.addHandler(file_handler)

    def _init_history_db(self):
        """初始化历史数据库"""
        try:
            with sqlite3.connect(self.history_db) as conn:
                cursor = conn.cursor()

                # 创建参数修改历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS parameter_changes (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        parameter_name TEXT NOT NULL,
                        importance TEXT NOT NULL,
                        old_value REAL,
                        new_value REAL NOT NULL,
                        change_amount REAL,
                        source TEXT NOT NULL,
                        success BOOLEAN NOT NULL,
                        timestamp TEXT NOT NULL,
                        device_id TEXT,
                        client_ip TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建客户端登录历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS client_logins (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        device_id TEXT NOT NULL,
                        client_ip TEXT NOT NULL,
                        web_port INTEGER NOT NULL,
                        server_type TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_param_name ON parameter_changes(parameter_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON parameter_changes(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_device_id ON parameter_changes(device_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_login_device ON client_logins(device_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_login_timestamp ON client_logins(timestamp)')

                conn.commit()

        except Exception as e:
            logging.error(f"初始化历史数据库失败: {e}")

    def log_parameter_change(self, param_name: str, old_value: Optional[float],
                           new_value: float, source: str = "unknown",
                           client_info: Optional[Dict[str, Any]] = None,
                           success: bool = True) -> bool:
        """记录参数修改 - 记录所有参数，不进行重要性过滤"""
        try:
            # 获取参数重要性（仅用于标记，不过滤）
            importance = self.classifier.get_importance(param_name)
            timestamp = datetime.now().isoformat()
            change_amount = new_value - old_value if old_value is not None else None

            # 构建日志信息
            log_data = {
                "parameter": param_name,
                "importance": importance.value,
                "old_value": old_value,
                "new_value": new_value,
                "change": change_amount,
                "source": source,
                "success": success,
                "timestamp": timestamp,
                "client_info": client_info or {}
            }

            # 保存到数据库
            self._save_to_history_db(log_data)

            # 格式化日志消息
            if old_value is not None:
                change_info = f"{old_value} -> {new_value} (变化: {new_value - old_value:+.3f})"
            else:
                change_info = f"设置为 {new_value}"

            status = "成功" if success else "失败"
            message = f"[{importance.value.upper()}] {param_name}: {change_info} | 来源: {source} | 状态: {status}"

            if client_info:
                device_id = client_info.get('device_id', 'unknown')
                client_ip = client_info.get('client_ip', 'unknown')
                message += f" | 设备: {device_id} | IP: {client_ip}"

            # 记录日志
            if success:
                self.logger.info(message)
            else:
                self.logger.error(message)

            return True

        except Exception as e:
            logging.error(f"记录参数修改日志失败: {e}")
            return False

    def _save_to_history_db(self, log_data: Dict[str, Any]):
        """保存到历史数据库"""
        try:
            with sqlite3.connect(self.history_db) as conn:
                cursor = conn.cursor()

                client_info = log_data.get('client_info', {})

                cursor.execute('''
                    INSERT INTO parameter_changes
                    (parameter_name, importance, old_value, new_value, change_amount,
                     source, success, timestamp, device_id, client_ip)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    log_data['parameter'],
                    log_data['importance'],
                    log_data['old_value'],
                    log_data['new_value'],
                    log_data['change'],
                    log_data['source'],
                    log_data['success'],
                    log_data['timestamp'],
                    client_info.get('device_id'),
                    client_info.get('client_ip')
                ))

                conn.commit()

        except Exception as e:
            logging.error(f"保存参数修改历史到数据库失败: {e}")

    def log_client_login(self, device_id: str, client_ip: str, web_port: int,
                        server_type: str = "unknown"):
        """记录客户端登录"""
        try:
            timestamp = datetime.now().isoformat()

            login_info = {
                "event": "client_login",
                "device_id": device_id,
                "client_ip": client_ip,
                "web_port": web_port,
                "server_type": server_type,
                "timestamp": timestamp
            }

            # 保存到数据库
            try:
                with sqlite3.connect(self.history_db) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO client_logins
                        (device_id, client_ip, web_port, server_type, timestamp)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (device_id, client_ip, web_port, server_type, timestamp))
                    conn.commit()
            except Exception as db_error:
                logging.error(f"保存客户端登录历史到数据库失败: {db_error}")

            message = f"客户端登录 | 设备: {device_id} | IP: {client_ip} | 端口: {web_port} | 服务器: {server_type}"
            self.logger.info(message)

        except Exception as e:
            logging.error(f"记录客户端登录日志失败: {e}")

    def set_min_importance(self, min_importance: ParameterImportance):
        """设置最小重要性级别"""
        self.min_importance = min_importance

    def get_parameter_history(self, param_name: str = None, limit: int = 100,
                            days: int = 7) -> List[Dict[str, Any]]:
        """获取参数修改历史"""
        try:
            with sqlite3.connect(self.history_db) as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_clause = "WHERE timestamp >= ?"
                params = [datetime.now() - timedelta(days=days)]

                if param_name:
                    where_clause += " AND parameter_name = ?"
                    params.append(param_name)

                query = f'''
                    SELECT parameter_name, importance, old_value, new_value,
                           change_amount, source, success, timestamp, device_id, client_ip
                    FROM parameter_changes
                    {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT ?
                '''
                params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()

                # 转换为字典列表
                columns = ['parameter_name', 'importance', 'old_value', 'new_value',
                          'change_amount', 'source', 'success', 'timestamp', 'device_id', 'client_ip']

                history = []
                for row in rows:
                    record = dict(zip(columns, row))
                    record['success'] = bool(record['success'])  # 转换布尔值
                    history.append(record)

                return history

        except Exception as e:
            logging.error(f"获取参数修改历史失败: {e}")
            return []

    def get_login_history(self, device_id: str = None, limit: int = 50,
                         days: int = 7) -> List[Dict[str, Any]]:
        """获取客户端登录历史"""
        try:
            with sqlite3.connect(self.history_db) as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_clause = "WHERE timestamp >= ?"
                params = [datetime.now() - timedelta(days=days)]

                if device_id:
                    where_clause += " AND device_id = ?"
                    params.append(device_id)

                query = f'''
                    SELECT device_id, client_ip, web_port, server_type, timestamp
                    FROM client_logins
                    {where_clause}
                    ORDER BY timestamp DESC
                    LIMIT ?
                '''
                params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()

                # 转换为字典列表
                columns = ['device_id', 'client_ip', 'web_port', 'server_type', 'timestamp']

                history = []
                for row in rows:
                    record = dict(zip(columns, row))
                    history.append(record)

                return history

        except Exception as e:
            logging.error(f"获取客户端登录历史失败: {e}")
            return []

    def get_parameter_stats(self) -> Dict[str, Any]:
        """获取参数统计信息"""
        try:
            stats = {
                "total_parameters": len(self.classifier.classification),
                "by_importance": {}
            }

            for importance in ParameterImportance:
                count = sum(1 for imp in self.classifier.classification.values()
                           if imp == importance.value)
                stats["by_importance"][importance.value] = count

            # 从数据库获取统计信息
            try:
                with sqlite3.connect(self.history_db) as conn:
                    cursor = conn.cursor()

                    # 获取最近7天的修改统计
                    cursor.execute('''
                        SELECT COUNT(*) as total_changes,
                               COUNT(DISTINCT parameter_name) as unique_params,
                               COUNT(DISTINCT device_id) as unique_devices
                        FROM parameter_changes
                        WHERE timestamp >= ?
                    ''', [datetime.now() - timedelta(days=7)])

                    row = cursor.fetchone()
                    if row:
                        stats["recent_changes"] = {
                            "total_changes": row[0],
                            "unique_parameters": row[1],
                            "unique_devices": row[2]
                        }

                    # 获取按重要性分组的修改统计
                    cursor.execute('''
                        SELECT importance, COUNT(*) as count
                        FROM parameter_changes
                        WHERE timestamp >= ?
                        GROUP BY importance
                    ''', [datetime.now() - timedelta(days=7)])

                    importance_stats = {}
                    for row in cursor.fetchall():
                        importance_stats[row[0]] = row[1]

                    stats["recent_changes_by_importance"] = importance_stats

            except Exception as db_error:
                logging.warning(f"获取数据库统计信息失败: {db_error}")

            return stats

        except Exception as e:
            logging.error(f"获取参数统计信息失败: {e}")
            return {}

    def set_min_importance(self, min_importance: ParameterImportance):
        """设置最小重要性级别"""
        self.min_importance = min_importance


# 全局参数日志记录器实例
_parameter_logger: Optional[ParameterLogger] = None


def get_parameter_logger() -> ParameterLogger:
    """获取全局参数日志记录器实例 - 默认记录所有参数"""
    global _parameter_logger
    if _parameter_logger is None:
        _parameter_logger = ParameterLogger()  # 使用默认设置，记录所有参数
    return _parameter_logger


def setup_parameter_logging(log_file: str = "logs/parameter_changes.log",
                          min_importance: ParameterImportance = ParameterImportance.SYSTEM) -> ParameterLogger:
    """设置参数日志系统 - 默认记录所有参数"""
    global _parameter_logger
    _parameter_logger = ParameterLogger(log_file, min_importance)
    return _parameter_logger
