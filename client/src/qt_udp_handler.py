#!/usr/bin/env python3
"""
基于Qt兼容UDP的参数处理器
使用Qt兼容的UDP客户端实现完全兼容的参数通信
"""

import asyncio
import logging
import os
from typing import List, Dict, Optional
from qt_compatible_udp import QtCompatibleUDP
from parameter_logger import get_parameter_logger


class QtUDPHandler:
    """基于Qt兼容UDP的参数处理器"""

    def __init__(self):
        """初始化处理器"""
        self.logger = logging.getLogger(__name__)

        # 从环境变量获取配置
        self.target_ip = os.getenv('UDP_TARGET_IP', '*************')
        self.target_port = int(os.getenv('UDP_TARGET_PORT', '8080'))
        self.listen_port = int(os.getenv('UDP_PORT', '18001'))

        # 创建Qt兼容的UDP客户端
        self.qt_client = QtCompatibleUDP()
        self.running = False

        # 去掉缓存机制，所有数据都实时读取

    async def start(self):
        """启动UDP处理器"""
        try:
            # 初始化Qt兼容的UDP客户端
            success = await self.qt_client.initialize(
                self.target_ip,
                self.target_port,
                self.listen_port
            )

            if success:
                self.running = True
                self.logger.info(f"Qt UDP处理器启动成功: {self.target_ip}:{self.target_port}")

                # 启动后立即读取一次参数进行缓存
                await self._initial_parameter_load()

                return True
            else:
                self.logger.error("Qt UDP处理器启动失败")
                return False

        except Exception as e:
            self.logger.error(f"启动Qt UDP处理器异常: {e}")
            return False

    async def stop(self):
        """停止UDP处理器"""
        try:
            self.running = False
            self.qt_client.close()
            self.logger.info("Qt UDP处理器已停止")
        except Exception as e:
            self.logger.error(f"停止Qt UDP处理器异常: {e}")

    async def reload_config(self, target_ip: str, target_port: int, local_port: int):
        """重新加载配置"""
        try:
            # 更新配置
            old_config = f"{self.target_ip}:{self.target_port}"
            self.target_ip = target_ip
            self.target_port = target_port
            self.listen_port = local_port

            # 如果配置有变化，重新初始化UDP客户端
            new_config = f"{self.target_ip}:{self.target_port}"
            if old_config != new_config:
                self.logger.info(f"UDP配置变化: {old_config} -> {new_config}")

                # 关闭旧连接
                self.qt_client.close()

                # 重新初始化
                success = await self.qt_client.initialize(
                    self.target_ip,
                    self.target_port,
                    self.listen_port
                )

                if success:
                    self.logger.info(f"UDP处理器配置重载成功: {new_config}")
                else:
                    self.logger.error(f"UDP处理器配置重载失败: {new_config}")
                    raise Exception("UDP处理器重新初始化失败")
            else:
                self.logger.info("UDP配置无变化，跳过重新初始化")

        except Exception as e:
            self.logger.error(f"重新加载UDP配置失败: {e}")
            raise

    async def _initial_parameter_load(self):
        """无缓存模式，不需要初始参数加载"""
        self.logger.info("无缓存模式启动完成，所有参数将实时读取")

    async def read_all_parameters(self) -> List[Dict[str, any]]:
        """读取所有参数（实时读取，不使用缓存）"""
        try:
            # 直接从下位机读取参数，不使用缓存
            self.logger.info("从下位机实时读取参数...")
            params = await self.qt_client.read_all_parameters()

            if params and len(params) > 0:
                self.logger.info(f"成功读取 {len(params)} 个参数")
                return params
            else:
                self.logger.warning("读取失败，未获取到参数")
                return []

        except Exception as e:
            self.logger.error(f"读取参数异常: {e}")
            return []



    async def write_parameter(self, name: str, value: float, source: str = "udp_handler") -> bool:
        """写入单个参数"""
        old_value = None
        try:
            # 先读取当前值用于日志记录
            try:
                old_value = await self.read_parameter(name, for_ping_test=True)
            except Exception as read_error:
                self.logger.debug(f"读取参数当前值失败，将记录为未知: {read_error}")

            self.logger.info(f"写入参数: {name} = {value}")

            # 使用Qt兼容的UDP客户端写入参数
            success = await self.qt_client.write_parameter(name, value)

            # 记录参数修改日志
            try:
                param_logger = get_parameter_logger()
                param_logger.log_parameter_change(
                    param_name=name,
                    old_value=old_value,
                    new_value=value,
                    source=source,
                    success=success
                )
            except Exception as log_error:
                self.logger.warning(f"记录参数修改日志失败: {log_error}")

            if success:
                self.logger.info(f"参数写入成功: {name} = {value}")
                return True
            else:
                self.logger.error(f"参数写入失败: {name} = {value}")
                return False

        except Exception as e:
            self.logger.error(f"写入参数异常: {e}")

            # 记录失败的参数修改日志
            try:
                param_logger = get_parameter_logger()
                param_logger.log_parameter_change(
                    param_name=name,
                    old_value=old_value,
                    new_value=value,
                    source=source,
                    success=False
                )
            except Exception as log_error:
                self.logger.warning(f"记录参数修改失败日志失败: {log_error}")

            return False

    async def read_parameter(self, name: str, for_ping_test: bool = False) -> Optional[float]:
        """读取单个参数

        Args:
            name: 参数名
            for_ping_test: 是否用于ping测试（如果是，则不降级到读取所有参数）
        """
        try:
            self.logger.info(f"读取单个参数: {name} (ping测试: {for_ping_test})")

            # 策略1: 尝试单个参数读取
            value = await self.qt_client.read_parameter(name)

            if value is not None:
                # 单个参数读取成功
                self.logger.info(f"单个参数读取成功: {name} = {value}")
                return value
            else:
                # 如果是ping测试，不要降级到读取所有参数（节省带宽）
                if for_ping_test:
                    self.logger.info(f"ping测试中单个参数读取失败: {name}")
                    return None

                # 策略2: 单个参数读取失败，读取所有参数获取最新值
                self.logger.info(f"单个参数读取失败，尝试读取所有参数获取最新值: {name}")

                # 读取所有参数
                params = await self.read_all_parameters()

                # 从参数中查找目标参数
                for param in params:
                    if param['name'] == name:
                        self.logger.info(f"从所有参数中获取: {name} = {param['value']}")
                        return param['value']

                self.logger.error(f"参数不存在: {name}")
                return None

        except Exception as e:
            self.logger.error(f"读取单个参数异常: {e}")
            return None



    def get_status(self) -> Dict[str, any]:
        """获取处理器状态"""
        return {
            "running": self.running,
            "target_ip": self.target_ip,
            "target_port": self.target_port,
            "listen_port": self.listen_port,
            "socket_connected": self.running,
            "cache_disabled": True  # 标记缓存已禁用
        }


# 测试函数
async def test_qt_udp_handler():
    """测试Qt UDP处理器"""
    handler = QtUDPHandler()

    try:
        # 启动处理器
        if not await handler.start():
            print("启动失败")
            return

        print("启动成功")

        # 读取所有参数
        params = await handler.read_all_parameters()
        print(f"读取到 {len(params)} 个参数")

        if params:
            # 显示前几个参数
            for param in params[:5]:
                print(f"  {param['name']} = {param['value']}")

            # 测试写入参数
            test_param = params[0]
            original_value = test_param['value']
            new_value = original_value + 0.1

            print(f"\n测试写入: {test_param['name']} = {new_value}")
            success = await handler.write_parameter(test_param['name'], new_value)
            print(f"写入结果: {'成功' if success else '失败'}")

            # 验证写入结果
            await asyncio.sleep(1)
            current_value = await handler.read_parameter(test_param['name'])
            print(f"验证结果: {test_param['name']} = {current_value}")

    finally:
        await handler.stop()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_qt_udp_handler())
