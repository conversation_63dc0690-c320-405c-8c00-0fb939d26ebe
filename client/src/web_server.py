#!/usr/bin/env python3
"""
Web服务器模块
提供参数修改的Web界面和API接口
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import json
import logging
from typing import Dict, Any
from pydantic import BaseModel

from qt_udp_handler import QtUDPHandler
from log_manager import get_log_manager
from parameter_logger import get_parameter_logger, ParameterImportance

# 全局变量
global_server_client = None


class ParameterUpdateRequest(BaseModel):
    """参数更新请求模型"""
    value: float


class AutoSetupRequest(BaseModel):
    """自动设置参数请求模型"""
    parameters: Dict[str, float] = {}


class WebServer:
    """Web服务器"""

    def _find_static_path(self):
        """查找静态文件路径"""
        possible_paths = [
            os.path.join(os.getcwd(), "static"),  # 当前工作目录
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "static"),  # 源码目录
            "static"  # 相对路径
        ]

        for path in possible_paths:
            if os.path.exists(path):
                self.logger.info(f"找到静态文件路径: {path}")
                return path

        self.logger.warning("未找到静态文件路径")
        return None

    def __init__(self, config_file: str = "config.json", server_client=None):
        """初始化Web服务器"""
        self.config = self.load_config(config_file)
        self.logger = logging.getLogger(__name__)

        # 服务器客户端引用
        self.server_client = server_client

        # 初始化静态文件路径（需要在logger初始化之后）
        self.static_path = self._find_static_path()

        # 创建FastAPI应用
        self.app = FastAPI(
            title="RK3588参数设置客户端",
            version="1.0.0",
            description="参数修改Web界面"
        )

        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # 挂载静态文件
        if self.static_path:
            self.app.mount("/static", StaticFiles(directory=self.static_path), name="static")

        # 创建Qt UDP处理器
        self.udp_handler = QtUDPHandler()

        # 设置路由
        self.setup_routes()

    def load_config(self, config_file: str) -> dict:
        """加载配置（优先使用环境变量）"""
        # 优先使用环境变量
        return {
            'device': {
                'id': os.getenv('DEVICE_ID', 'RK3588_001'),
                'web_port': int(os.getenv('WEB_PORT', '7001'))
            }
        }

    def setup_routes(self):
        """设置路由"""

        @self.app.get("/")
        async def index():
            """主页"""
            if self.static_path:
                index_file = os.path.join(self.static_path, "index.html")
                if os.path.exists(index_file):
                    return FileResponse(index_file)
            return {"message": "RK3588参数设置客户端", "status": "running"}

        @self.app.get("/log-manager.html")
        async def log_manager():
            """日志管理页面"""
            if self.static_path:
                log_manager_file = os.path.join(self.static_path, "log-manager.html")
                if os.path.exists(log_manager_file):
                    return FileResponse(log_manager_file)
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "日志管理页面不存在"}
            )

        @self.app.get("/log-manager.js")
        async def log_manager_js():
            """日志管理页面JavaScript"""
            if self.static_path:
                js_file = os.path.join(self.static_path, "log-manager.js")
                if os.path.exists(js_file):
                    return FileResponse(js_file, media_type="application/javascript")
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "JavaScript文件不存在"}
            )

        @self.app.get("/api/status")
        async def get_status():
            """获取系统状态"""
            try:
                udp_status = self.udp_handler.get_status()
                return {
                    "success": True,
                    "data": {
                        "web_server": "running",
                        "udp_handler": udp_status,
                        "timestamp": self.get_current_time()
                    }
                }
            except Exception as e:
                self.logger.error(f"获取状态失败: {e}")
                raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

        @self.app.get("/api/device-info")
        async def get_device_info():
            """获取设备信息"""
            try:
                return {
                    "success": True,
                    "data": {
                        "device_id": self.config['device']['id'],
                        "web_port": self.config['device']['web_port'],
                        "version": "1.0.0"
                    }
                }
            except Exception as e:
                self.logger.error(f"获取设备信息失败: {e}")
                raise HTTPException(status_code=500, detail=f"获取设备信息失败: {str(e)}")

        @self.app.get("/api/ping-device")
        async def ping_device():
            """检测下位机连接状态并刷新参数显示（无缓存模式）"""
            try:
                # 直接调用读取所有参数的API逻辑，简单可靠
                import time
                start_time = time.time()

                # 直接读取所有参数，不使用缓存
                params = await self.udp_handler.read_all_parameters()
                test_duration = time.time() - start_time

                # 获取目标信息
                target_ip = os.getenv('UDP_TARGET_IP', '*************')
                target_port = int(os.getenv('UDP_TARGET_PORT', '8080'))

                if params and len(params) > 0:
                    # 成功读取到参数，说明下位机在线
                    return {
                        "success": True,
                        "data": {
                            "online": True,
                            "target_ip": target_ip,
                            "target_port": target_port,
                            "connection_status": f"通信正常，读取到{len(params)}个参数",
                            "test_method": "Read all parameters (no cache)",
                            "test_duration": f"{test_duration:.2f}s",
                            "parameter_count": len(params),
                            "parameters": params,  # 返回参数数据供前端更新显示
                            "last_update": self.get_current_time(),
                            "data_source": "real_time"
                        }
                    }
                else:
                    # 没有读取到参数，说明下位机离线
                    return {
                        "success": True,
                        "data": {
                            "online": False,
                            "target_ip": target_ip,
                            "target_port": target_port,
                            "connection_status": "设备离线，无法读取参数",
                            "test_method": "Read all parameters (no cache)",
                            "test_duration": f"{test_duration:.2f}s",
                            "parameter_count": 0,
                            "parameters": [],
                            "last_update": self.get_current_time(),
                            "data_source": "none"
                        }
                    }

            except Exception as e:
                self.logger.error(f"设备连接检测失败: {e}")
                return {
                    "success": True,
                    "data": {
                        "online": False,
                        "target_ip": os.getenv('UDP_TARGET_IP', '*************'),
                        "target_port": int(os.getenv('UDP_TARGET_PORT', '8080')),
                        "connection_status": f"检测异常: {str(e)}",
                        "test_method": "Read all parameters (no cache)",
                        "parameters": [],
                        "parameter_count": 0,
                        "last_update": self.get_current_time(),
                        "data_source": "error"
                    }
                }

        @self.app.get("/api/log-info")
        async def get_log_info():
            """获取日志信息"""
            try:
                log_manager = get_log_manager()
                log_info = log_manager.get_log_info()

                return {
                    "success": True,
                    "data": log_info
                }
            except Exception as e:
                self.logger.error(f"获取日志信息失败: {e}")
                return {
                    "success": False,
                    "message": f"获取日志信息失败: {str(e)}"
                }

        @self.app.post("/api/log-rotate")
        async def rotate_log():
            """手动执行日志轮转"""
            try:
                log_manager = get_log_manager()
                log_manager.force_rotate()

                return {
                    "success": True,
                    "message": "日志轮转执行成功"
                }
            except Exception as e:
                self.logger.error(f"执行日志轮转失败: {e}")
                return {
                    "success": False,
                    "message": f"执行日志轮转失败: {str(e)}"
                }

        @self.app.post("/api/log-config")
        async def update_log_config(request: Request):
            """更新日志配置"""
            try:
                data = await request.json()

                # 验证参数
                max_size_mb = data.get('max_size_mb')
                backup_count = data.get('backup_count')
                log_level = data.get('log_level')

                if max_size_mb is not None:
                    if not isinstance(max_size_mb, (int, float)) or max_size_mb <= 0 or max_size_mb > 1000:
                        return {
                            "success": False,
                            "message": "最大文件大小必须在0-1000MB之间"
                        }

                if backup_count is not None:
                    if not isinstance(backup_count, int) or backup_count < 0 or backup_count > 100:
                        return {
                            "success": False,
                            "message": "备份文件数量必须在0-100之间"
                        }

                if log_level is not None:
                    valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
                    if log_level not in valid_levels:
                        return {
                            "success": False,
                            "message": f"日志级别必须是以下之一: {', '.join(valid_levels)}"
                        }

                # 更新配置
                log_manager = get_log_manager()
                updated = log_manager.update_config(
                    max_size_mb=max_size_mb,
                    backup_count=backup_count,
                    log_level=log_level
                )

                if updated:
                    return {
                        "success": True,
                        "message": "日志配置更新成功",
                        "data": log_manager.get_log_info()
                    }
                else:
                    return {
                        "success": False,
                        "message": "日志配置更新失败"
                    }

            except Exception as e:
                self.logger.error(f"更新日志配置失败: {e}")
                return {
                    "success": False,
                    "message": f"更新日志配置失败: {str(e)}"
                }

        @self.app.get("/api/log-content/{filename}")
        async def get_log_content(filename: str, lines: int = 100):
            """获取日志文件内容"""
            try:
                # 安全检查：只允许读取logs目录下的.log文件
                if not filename.endswith('.log') or '/' in filename or '\\' in filename:
                    return {
                        "success": False,
                        "message": "无效的文件名"
                    }

                # 构建文件路径
                log_dir = "logs"
                file_path = os.path.join(log_dir, filename)

                if not os.path.exists(file_path):
                    return {
                        "success": False,
                        "message": "文件不存在"
                    }

                # 读取文件内容（最后N行）
                content_lines = []
                file_size = os.path.getsize(file_path)

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    all_lines = f.readlines()

                    # 获取最后N行
                    if lines > 0:
                        content_lines = all_lines[-lines:]
                    else:
                        content_lines = all_lines

                return {
                    "success": True,
                    "data": {
                        "filename": filename,
                        "file_size": file_size,
                        "total_lines": len(all_lines),
                        "displayed_lines": len(content_lines),
                        "content": ''.join(content_lines)
                    }
                }

            except Exception as e:
                self.logger.error(f"读取日志文件失败: {e}")
                return {
                    "success": False,
                    "message": f"读取日志文件失败: {str(e)}"
                }

        @self.app.get("/api/parameters")
        async def get_parameters(force_refresh: bool = False):
            """获取所有参数

            Args:
                force_refresh: 是否强制刷新，不使用缓存
            """
            try:
                parameters = await self.udp_handler.read_all_parameters()
                return {
                    "success": True,
                    "data": parameters,
                    "count": len(parameters),
                    "timestamp": self.get_current_time()
                }
            except Exception as e:
                self.logger.error(f"获取参数失败: {e}")
                return {
                    "success": False,
                    "message": f"获取参数失败: {str(e)}",
                    "data": []
                }

        @self.app.post("/api/parameters/{param_name}")
        async def update_parameter(param_name: str, request: ParameterUpdateRequest, http_request: Request):
            """更新单个参数"""
            try:
                # 获取客户端信息
                client_ip = http_request.client.host if http_request.client else "unknown"
                user_agent = http_request.headers.get("user-agent", "unknown")

                # 构建来源信息
                source = f"web_api({client_ip})"

                success = await self.udp_handler.write_parameter(param_name, request.value, source)

                if success:
                    return {
                        "success": True,
                        "message": f"参数 {param_name} 写入请求已发送",
                        "data": {
                            "name": param_name,
                            "value": request.value,
                            "timestamp": self.get_current_time(),
                            "note": "请重新读取参数以验证写入结果"
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": f"参数 {param_name} 写入请求发送失败"
                    }

            except Exception as e:
                self.logger.error(f"更新参数失败: {e}")
                return {
                    "success": False,
                    "message": f"更新参数失败: {str(e)}"
                }

        @self.app.get("/api/parameter-history")
        async def get_parameter_history(param_name: str = None, limit: int = 100, days: int = 7):
            """获取参数修改历史"""
            try:
                param_logger = get_parameter_logger()
                history = param_logger.get_parameter_history(param_name, limit, days)

                return {
                    "success": True,
                    "data": history,
                    "count": len(history),
                    "filters": {
                        "parameter": param_name,
                        "limit": limit,
                        "days": days
                    }
                }
            except Exception as e:
                self.logger.error(f"获取参数历史失败: {e}")
                return {
                    "success": False,
                    "message": f"获取参数历史失败: {str(e)}"
                }

        @self.app.get("/api/login-history")
        async def get_login_history(device_id: str = None, limit: int = 50, days: int = 7):
            """获取客户端登录历史"""
            try:
                param_logger = get_parameter_logger()
                history = param_logger.get_login_history(device_id, limit, days)

                return {
                    "success": True,
                    "data": history,
                    "count": len(history),
                    "filters": {
                        "device_id": device_id,
                        "limit": limit,
                        "days": days
                    }
                }
            except Exception as e:
                self.logger.error(f"获取登录历史失败: {e}")
                return {
                    "success": False,
                    "message": f"获取登录历史失败: {str(e)}"
                }

        @self.app.get("/api/parameter-stats")
        async def get_parameter_stats():
            """获取参数统计信息"""
            try:
                param_logger = get_parameter_logger()
                stats = param_logger.get_parameter_stats()

                return {
                    "success": True,
                    "data": stats
                }
            except Exception as e:
                self.logger.error(f"获取参数统计失败: {e}")
                return {
                    "success": False,
                    "message": f"获取参数统计失败: {str(e)}"
                }

        @self.app.get("/api/parameter-log-config")
        async def get_parameter_log_config():
            """获取参数日志配置信息"""
            try:
                param_logger = get_parameter_logger()

                return {
                    "success": True,
                    "message": "参数日志配置信息",
                    "data": {
                        "logging_enabled": True,
                        "log_all_parameters": True,
                        "description": "记录所有参数的修改，不进行重要性过滤",
                        "log_file": param_logger.log_file,
                        "history_db": param_logger.history_db
                    }
                }
            except Exception as e:
                self.logger.error(f"获取参数日志配置失败: {e}")
                return {
                    "success": False,
                    "message": f"获取参数日志配置失败: {str(e)}"
                }

        @self.app.get("/api/parameters/{param_name}")
        async def get_parameter(param_name: str):
            """获取单个参数"""
            try:
                value = await self.udp_handler.read_parameter(param_name)

                if value is not None:
                    return {
                        "success": True,
                        "data": {
                            "name": param_name,
                            "value": value,
                            "type": "float",
                            "timestamp": self.get_current_time()
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": f"参数 {param_name} 不存在或读取失败"
                    }

            except Exception as e:
                self.logger.error(f"获取参数失败: {e}")
                return {
                    "success": False,
                    "message": f"获取参数失败: {str(e)}"
                }

        @self.app.post("/api/auto-setup")
        async def auto_setup_parameters(request: AutoSetupRequest = None, http_request: Request = None):
            """自动设置参数 - 使用环境变量配置"""
            try:
                # 如果没有提供参数，从环境变量加载
                if not request or not request.parameters:
                    parameters = self.load_auto_setup_params()
                else:
                    parameters = request.parameters

                if not parameters:
                    return {
                        "success": False,
                        "message": "没有找到要设置的参数配置"
                    }

                self.logger.info(f"收到自动设置参数请求: {parameters}")

                # 1. 获取所有参数
                all_parameters = await self.udp_handler.read_all_parameters()
                if not all_parameters:
                    return {
                        "success": False,
                        "message": "无法获取参数列表"
                    }

                # 创建参数名到参数对象的映射
                param_map = {param['name']: param for param in all_parameters}

                results = []
                success_count = 0

                # 2. 逐个设置参数
                for param_name, target_value in parameters.items():
                    try:
                        if param_name not in param_map:
                            results.append({
                                "param_name": param_name,
                                "success": False,
                                "message": f"参数 {param_name} 不存在"
                            })
                            continue

                        current_param = param_map[param_name]
                        current_value = current_param['value']

                        self.logger.info(f"设置参数 {param_name}: {current_value} -> {target_value}")

                        # 检查是否需要修改
                        if abs(current_value - target_value) < 0.0001:
                            results.append({
                                "param_name": param_name,
                                "success": True,
                                "current_value": current_value,
                                "target_value": target_value,
                                "changed": False,
                                "message": f"参数 {param_name} 已经是目标值"
                            })
                            success_count += 1
                            continue

                        # 获取客户端信息
                        client_ip = "unknown"
                        if http_request and http_request.client:
                            client_ip = http_request.client.host

                        # 写入新值
                        source = f"auto_setup({client_ip})"
                        write_success = await self.udp_handler.write_parameter(param_name, target_value, source)

                        if write_success:
                            # 验证写入结果
                            import asyncio
                            await asyncio.sleep(0.5)  # 等待下位机处理

                            new_value = await self.udp_handler.read_parameter(param_name)
                            verified = new_value is not None and abs(new_value - target_value) < 0.0001

                            results.append({
                                "param_name": param_name,
                                "success": True,
                                "old_value": current_value,
                                "target_value": target_value,
                                "new_value": new_value,
                                "changed": True,
                                "verified": verified,
                                "message": f"参数 {param_name} 设置成功"
                            })
                            success_count += 1
                        else:
                            results.append({
                                "param_name": param_name,
                                "success": False,
                                "message": f"参数 {param_name} 写入失败"
                            })

                    except Exception as e:
                        results.append({
                            "param_name": param_name,
                            "success": False,
                            "message": f"设置参数 {param_name} 时发生错误: {str(e)}"
                        })

                return {
                    "success": success_count > 0,
                    "message": f"设置完成，成功 {success_count}/{len(parameters)} 个参数",
                    "data": {
                        "total": len(parameters),
                        "success_count": success_count,
                        "results": results,
                        "timestamp": self.get_current_time()
                    }
                }

            except Exception as e:
                self.logger.error(f"自动设置参数失败: {e}")
                return {
                    "success": False,
                    "message": f"自动设置参数失败: {str(e)}"
                }

        @self.app.get("/api/parameter-mappings")
        async def get_parameter_mappings():
            """获取参数映射"""
            try:
                mappings_file = "parameter_mappings.json"
                if os.path.exists(mappings_file):
                    with open(mappings_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    return {
                        "success": True,
                        "data": data
                    }
                else:
                    # 返回空映射
                    return {
                        "success": True,
                        "data": {
                            "mappings": {},
                            "last_updated": self.get_current_time(),
                            "version": "1.0.0"
                        }
                    }
            except Exception as e:
                self.logger.error(f"获取参数映射失败: {e}")
                return {
                    "success": False,
                    "message": f"获取参数映射失败: {str(e)}"
                }

        @self.app.post("/api/parameter-mappings")
        async def save_parameter_mappings(request: Request):
            """保存参数映射"""
            try:
                data = await request.json()
                mappings_file = "parameter_mappings.json"

                with open(mappings_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                self.logger.info(f"✅ 参数映射已保存，包含 {len(data.get('mappings', {}))} 个映射")

                return {
                    "success": True,
                    "message": "参数映射保存成功",
                    "count": len(data.get('mappings', {}))
                }
            except Exception as e:
                self.logger.error(f"保存参数映射失败: {e}")
                return {
                    "success": False,
                    "message": f"保存参数映射失败: {str(e)}"
                }

        @self.app.get("/config.html")
        async def config_page():
            """配置管理页面"""
            if self.static_path:
                config_file = os.path.join(self.static_path, "config.html")
                if os.path.exists(config_file):
                    return FileResponse(config_file)
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "配置页面不存在"}
            )

        @self.app.get("/api/config")
        async def get_config():
            """获取当前配置"""
            try:
                config = self.load_env_config()
                return {
                    "success": True,
                    "data": config
                }
            except Exception as e:
                self.logger.error(f"获取配置失败: {e}")
                return {
                    "success": False,
                    "message": f"获取配置失败: {str(e)}"
                }

        @self.app.post("/api/config")
        async def save_config(request: Request):
            """保存配置"""
            try:
                data = await request.json()

                # 保存到.env文件
                self.save_env_config(data)

                # 重新加载环境变量
                self.reload_env_variables(data)

                # 重新加载应用配置
                await self.reload_application_config(data)

                self.logger.info("✅ 配置已保存并重新加载")

                return {
                    "success": True,
                    "message": "配置保存成功，应用配置已更新"
                }
            except Exception as e:
                self.logger.error(f"保存配置失败: {e}")
                return {
                    "success": False,
                    "message": f"保存配置失败: {str(e)}"
                }

        @self.app.get("/api/server-status")
        async def get_server_status():
            """获取服务器连接状态"""
            try:
                if self.server_client or global_server_client:
                    # 从实际的服务器客户端获取状态
                    client = self.server_client or global_server_client
                    status_data = client.get_status()
                    return {
                        "success": True,
                        "data": status_data
                    }
                else:
                    # 如果没有服务器客户端，返回默认状态
                    status_data = {
                        "servers": [
                            {
                                "name": "default",
                                "host": "**************",
                                "port": 7777,
                                "connected": False,
                                "last_heartbeat": None
                            },
                            {
                                "name": "custom",
                                "host": os.getenv('SERVER_HOST', '*************'),
                                "port": int(os.getenv('SERVER_PORT', '8888')),
                                "connected": False,
                                "last_heartbeat": None
                            }
                        ],
                        "connected_count": 0
                    }

                    return {
                        "success": True,
                        "data": status_data
                    }
            except Exception as e:
                self.logger.error(f"获取服务器状态失败: {e}")
                return {
                    "success": False,
                    "message": f"获取服务器状态失败: {str(e)}"
                }

        @self.app.post("/api/reload-config")
        async def reload_config():
            """重新加载配置"""
            try:
                # 重新加载环境变量
                import importlib
                import sys

                # 重新读取.env文件
                env_file = ".env"
                if os.path.exists(env_file):
                    with open(env_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#') and '=' in line:
                                key, value = line.split('=', 1)
                                os.environ[key.strip()] = value.strip()

                # 重新加载配置
                if hasattr(self, 'config'):
                    # 重新读取配置文件
                    config_file = getattr(self, 'config_file', 'config.json')
                    if os.path.exists(config_file):
                        with open(config_file, 'r', encoding='utf-8') as f:
                            self.config = json.load(f)

                self.logger.info("配置已重新加载")

                return {
                    "success": True,
                    "message": "配置重新加载成功",
                    "device_id": os.getenv('DEVICE_ID', 'Unknown'),
                    "timestamp": self.get_current_time()
                }
            except Exception as e:
                self.logger.error(f"重新加载配置失败: {e}")
                return {
                    "success": False,
                    "message": f"重新加载配置失败: {str(e)}"
                }

        @self.app.post("/api/restart-service")
        async def restart_service(request: Request):
            """重启服务"""
            try:
                import subprocess

                # 获取密码
                data = await request.json()
                password = data.get('password', '')

                if not password:
                    return {
                        "success": False,
                        "message": "请提供管理员密码"
                    }

                self.logger.info("收到重启服务请求")

                # 使用echo密码 | sudo -S 的方式执行命令
                try:
                    # 先验证密码是否正确
                    test_cmd = f'echo "{password}" | sudo -S -v'
                    test_process = subprocess.run(
                        test_cmd,
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=3
                    )

                    if test_process.returncode != 0:
                        self.logger.error("密码验证失败")
                        return {
                            "success": False,
                            "message": "密码错误，请检查管理员密码"
                        }

                    # 密码正确，延迟执行重启命令，给响应时间
                    # 使用nohup和后台执行，避免进程被当前服务终止影响
                    restart_cmd = f'bash -c "sleep 2 && echo \\"{password}\\" | sudo -S systemctl restart param-set-client.service" &'

                    # 使用Popen启动后台进程，不等待结果
                    subprocess.Popen(
                        restart_cmd,
                        shell=True,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        preexec_fn=os.setsid if hasattr(os, 'setsid') else None
                    )

                    self.logger.info("服务重启命令已发送")
                    return {
                        "success": True,
                        "message": "服务重启命令已发送，服务将在几秒钟内重启"
                    }

                except subprocess.TimeoutExpired:
                    self.logger.error("密码验证超时")
                    return {
                        "success": False,
                        "message": "密码验证超时，请重试"
                    }
                except Exception as e:
                    self.logger.error(f"执行重启命令失败: {e}")
                    return {
                        "success": False,
                        "message": f"执行重启命令失败: {str(e)}"
                    }

            except Exception as e:
                self.logger.error(f"重启服务失败: {e}")
                return {
                    "success": False,
                    "message": f"重启服务失败: {str(e)}"
                }

        @self.app.post("/api/restart-system")
        async def restart_system(request: Request):
            """重启计算机"""
            try:
                import subprocess

                # 获取密码
                data = await request.json()
                password = data.get('password', '')

                if not password:
                    return {
                        "success": False,
                        "message": "请提供管理员密码"
                    }

                self.logger.warning("收到重启系统请求")

                # 使用echo密码 | sudo -S 的方式执行重启命令
                try:
                    # 先验证密码是否正确
                    test_cmd = f'echo "{password}" | sudo -S -v'
                    test_process = subprocess.run(
                        test_cmd,
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=5
                    )

                    if test_process.returncode != 0:
                        self.logger.error("密码验证失败")
                        return {
                            "success": False,
                            "message": "密码错误，请检查管理员密码"
                        }

                    # 密码正确，执行重启命令
                    # 延迟3秒执行重启，给响应时间
                    reboot_cmd = f'bash -c "sleep 3 && echo \\"{password}\\" | sudo -S reboot"'
                    subprocess.Popen(
                        reboot_cmd,
                        shell=True,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )

                    self.logger.warning("系统重启命令已发送")

                    return {
                        "success": True,
                        "message": "系统重启命令已发送，计算机将在几秒钟内重启"
                    }

                except subprocess.TimeoutExpired:
                    self.logger.error("密码验证超时")
                    return {
                        "success": False,
                        "message": "密码验证超时，请重试"
                    }
                except Exception as e:
                    self.logger.error(f"执行重启命令失败: {e}")
                    return {
                        "success": False,
                        "message": f"执行重启命令失败: {str(e)}"
                    }

            except Exception as e:
                self.logger.error(f"重启系统失败: {e}")
                return {
                    "success": False,
                    "message": f"重启系统失败: {str(e)}"
                }

        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy",
                "service": "RK3588参数设置客户端",
                "version": "1.0.0",
                "timestamp": self.get_current_time()
            }

        # 错误处理
        @self.app.exception_handler(404)
        async def not_found_handler(request: Request, exc):
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "页面不存在"}
            )

        @self.app.exception_handler(500)
        async def internal_error_handler(request: Request, exc):
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "服务器内部错误"}
            )

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().isoformat()

    def load_auto_setup_params(self) -> Dict[str, float]:
        """加载自动设置参数配置"""
        params = {}

        # 查找.env文件的可能位置
        possible_paths = [
            ".env",  # 当前工作目录
            "../.env",  # 上级目录
            os.path.join(os.path.dirname(__file__), ".env"),  # src目录
            os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'),  # 项目根目录
            os.path.join(os.getcwd(), '.env'),  # 当前工作目录的绝对路径
        ]

        env_file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                env_file_path = path
                break

        try:
            if env_file_path:
                self.logger.info(f"读取配置文件: {env_file_path}")
                with open(env_file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            else:
                self.logger.warning("未找到.env文件")
                return params

            # 定义系统配置项，这些不是参数
            system_configs = {
                'WEB_PORT', 'UDP_PORT', 'SERVER_HOST', 'SERVER_PORT',
                'DEVICE_ID', 'UDP_TARGET_IP', 'UDP_TARGET_PORT', 'LOG_LEVEL'
            }

            # 解析.env文件，只处理参数配置部分
            in_param_section = False
            for line_num, line in enumerate(lines, 1):
                original_line = line
                line = line.strip()
                self.logger.debug(f"处理第{line_num}行: '{original_line.rstrip()}'")

                if not line:
                    self.logger.debug(f"第{line_num}行: 空行，跳过")
                    continue

                # 检查是否进入参数配置部分
                if line.startswith('# 自动参数设置配置'):
                    in_param_section = True
                    self.logger.info(f"第{line_num}行: 进入参数配置部分")
                    continue

                # 如果遇到新的注释段落，退出参数配置部分
                if line.startswith('#') and in_param_section and '自动参数设置' not in line:
                    self.logger.info(f"第{line_num}行: 遇到新注释段落，退出参数配置部分")
                    break

                # 跳过注释行
                if line.startswith('#'):
                    self.logger.debug(f"第{line_num}行: 注释行，跳过")
                    continue

                # 只在参数配置部分处理配置项
                if in_param_section and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    self.logger.info(f"第{line_num}行: 找到参数配置 {key}={value}")

                    # 跳过系统配置项
                    if key in system_configs:
                        self.logger.info(f"第{line_num}行: 跳过系统配置项 {key}")
                        continue

                    # 尝试将值转换为float
                    try:
                        float_value = float(value)
                        params[key] = float_value
                        self.logger.info(f"加载参数配置: {key} = {float_value}")
                    except ValueError:
                        self.logger.warning(f"跳过无效参数配置: {key} = {value}")
                        continue
                elif '=' in line:
                    self.logger.debug(f"第{line_num}行: 配置项但不在参数配置部分: {line}")
                else:
                    self.logger.debug(f"第{line_num}行: 非配置项: {line}")



        except Exception as e:
            self.logger.error(f"读取参数配置失败: {e}")
            # 使用空的默认配置
            params = {}
            self.logger.info("使用空的默认参数配置")

        self.logger.info(f"最终加载的参数配置: {params}")
        return params

    def load_env_config(self):
        """加载环境配置"""
        # 默认配置
        default_config = {
            'WEB_PORT': '7002',
            'UDP_PORT': '18001',
            'SERVER_HOST': '**************0',
            'SERVER_PORT': '8888',
            'DEVICE_ID': '太阳能通信车001',
            'UDP_TARGET_IP': '*************',
            'UDP_TARGET_PORT': '8080',
            'LOG_LEVEL': 'INFO'
        }

        # 从环境变量读取当前配置
        config = {}
        for key, default_value in default_config.items():
            config[key] = os.getenv(key, default_value)

        return config

    def save_env_config(self, config):
        """保存配置到.env文件"""
        env_file = ".env"

        # 读取现有的.env文件内容
        existing_lines = []
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                existing_lines = f.readlines()

        # 更新配置
        updated_lines = []
        config_keys = set(config.keys())

        # 处理现有行
        for line in existing_lines:
            line = line.rstrip('\n')
            if '=' in line and not line.strip().startswith('#'):
                key = line.split('=', 1)[0].strip()
                if key in config:
                    # 更新现有配置
                    updated_lines.append(f"{key}={config[key]}")
                    config_keys.remove(key)
                else:
                    # 保留其他配置
                    updated_lines.append(line)
            else:
                # 保留注释和空行
                updated_lines.append(line)

        # 添加新配置
        for key in config_keys:
            updated_lines.append(f"{key}={config[key]}")

        # 写入文件
        with open(env_file, 'w', encoding='utf-8') as f:
            for line in updated_lines:
                f.write(line + '\n')

    def reload_env_variables(self, config):
        """重新加载环境变量"""
        for key, value in config.items():
            os.environ[key] = str(value)

    async def reload_application_config(self, config):
        """重新加载应用配置"""
        try:
            # 更新内部配置
            if hasattr(self, 'config'):
                # 更新设备配置
                if 'device' in self.config:
                    self.config['device']['id'] = config.get('DEVICE_ID', self.config['device'].get('id', '太阳能通信车001'))
                    self.config['device']['web_port'] = int(config.get('WEB_PORT', self.config['device'].get('web_port', 7002)))

                # 更新UDP目标配置
                if 'udp' in self.config:
                    self.config['udp']['target_ip'] = config.get('UDP_TARGET_IP', self.config['udp'].get('target_ip', '*************'))
                    self.config['udp']['target_port'] = int(config.get('UDP_TARGET_PORT', self.config['udp'].get('target_port', 8080)))
                    self.config['udp']['port'] = int(config.get('UDP_PORT', self.config['udp'].get('port', 18001)))

                # 更新服务器配置
                if 'server' in self.config:
                    self.config['server']['host'] = config.get('SERVER_HOST', self.config['server'].get('host', '*************'))
                    self.config['server']['port'] = int(config.get('SERVER_PORT', self.config['server'].get('port', 8888)))

            # 重新配置UDP处理器
            if hasattr(self, 'udp_handler') and self.udp_handler:
                await self.udp_handler.reload_config(
                    target_ip=config.get('UDP_TARGET_IP', '*************'),
                    target_port=int(config.get('UDP_TARGET_PORT', 8080)),
                    local_port=int(config.get('UDP_PORT', 18001))
                )
                self.logger.info(f"UDP处理器配置已更新: {config.get('UDP_TARGET_IP')}:{config.get('UDP_TARGET_PORT')}")

            self.logger.info("应用配置重新加载完成")

        except Exception as e:
            self.logger.error(f"重新加载应用配置失败: {e}")
            raise

    async def start(self):
        """启动Web服务器"""
        try:
            # 启动UDP处理器
            await self.udp_handler.start()

            # 启动Web服务器
            port = self.config['device']['web_port']
            config = uvicorn.Config(
                self.app,
                host="0.0.0.0",
                port=port,
                log_level="info",
                access_log=True
            )
            server = uvicorn.Server(config)
            self.logger.info(f"Web服务器启动，访问 http://localhost:{port}")
            await server.serve()

        except Exception as e:
            self.logger.error(f"启动Web服务器失败: {e}")
            raise
        finally:
            # 停止UDP处理器
            await self.udp_handler.stop()


def set_global_server_client(server_client):
    """设置全局服务器客户端引用"""
    global global_server_client
    global_server_client = server_client

async def start_web_server(config_file: str = "config.json", server_client=None):
    """启动Web服务器的便捷函数"""
    web_server = WebServer(config_file, server_client)
    await web_server.start()


if __name__ == "__main__":
    import asyncio

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    asyncio.run(start_web_server())
