{"classification": {"max_rpm": "critical", "max_acc": "critical", "maxTorq": "critical", "feedPwr": "critical", "dispPwr": "critical", "VehMass": "critical", "gRatio": "critical", "spd_kp": "important", "spd_ki": "important", "spd_kd": "important", "spd_il": "important", "spd_ol": "important", "crv_kp": "important", "crv_ki": "important", "crv_kd": "important", "crv_il": "important", "crv_ol": "important", "brk_on": "important", "brk_off": "important", "brk_pos": "important", "brk_rev": "important", "prCTime": "important", "Ospd_kp": "important", "Ospd_ki": "important", "Ospd_kd": "important", "Ospd_il": "important", "Ospd_ol": "important", "Ocrv_kp": "important", "Ocrv_ki": "important", "Ocrv_kd": "important", "Ocrv_il": "important", "Ocrv_ol": "important", "whl_bas": "important", "whl_dia": "important", "sleepTm": "debug", "wakeTm": "debug", "test": "debug", "minTorq": "debug", "mot_kp": "debug", "mot_ki": "debug", "mot_kd": "debug", "mot_il": "debug", "mot_ol": "debug", "diff_sp": "debug", "pwr_sta": "system", "high_sw": "system", "stop_sw": "system", "lightSt": "system", "pwr_btn": "system"}, "last_updated": "2025-08-02T00:00:00.000Z", "description": "参数重要性分类配置", "importance_levels": {"critical": "核心参数，必须记录，影响车辆基本性能和安全", "important": "重要参数，建议记录，影响车辆控制和功能", "debug": "调试参数，可选记录，用于测试和调试", "system": "系统参数，简化记录，主要是状态信息"}}