#!/usr/bin/env python3
"""
参数日志功能测试脚本
测试新的参数修改日志记录功能
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from parameter_logger import ParameterLogger, ParameterImportance, get_parameter_logger, setup_parameter_logging


def test_parameter_classification():
    """测试参数分类功能"""
    print("=" * 60)
    print("测试参数分类功能")
    print("=" * 60)

    param_logger = get_parameter_logger()
    classifier = param_logger.classifier

    # 测试不同类型的参数
    test_params = [
        "max_rpm",      # critical
        "spd_kp",       # important
        "sleepTm",      # debug
        "pwr_sta",      # system
        "unknown_param" # 未知参数，应该默认为debug
    ]

    for param in test_params:
        importance = classifier.get_importance(param)
        print(f"参数 {param:12} -> 重要性: {importance.value}")

    print()


def test_importance_filtering():
    """测试重要性过滤功能"""
    print("=" * 60)
    print("测试重要性过滤功能")
    print("=" * 60)

    # 创建不同重要性级别的日志记录器
    test_levels = [
        ParameterImportance.CRITICAL,
        ParameterImportance.IMPORTANT,
        ParameterImportance.DEBUG,
        ParameterImportance.SYSTEM
    ]

    # 获取临时目录
    import tempfile
    temp_dir = tempfile.gettempdir()

    for min_level in test_levels:
        print(f"\n设置最小重要性级别为: {min_level.value}")
        param_logger = ParameterLogger(
            log_file=os.path.join(temp_dir, f"test_{min_level.value}.log"),
            min_importance=min_level,
            history_db=os.path.join(temp_dir, f"test_{min_level.value}.db")
        )

        # 测试不同重要性的参数
        test_cases = [
            ("max_rpm", 1500.0, "critical"),
            ("spd_kp", 0.5, "important"),
            ("sleepTm", 10.0, "debug"),
            ("pwr_sta", 1.0, "system")
        ]

        for param_name, value, expected_importance in test_cases:
            logged = param_logger.log_parameter_change(
                param_name=param_name,
                old_value=None,
                new_value=value,
                source="test_script",
                success=True
            )
            print(f"  {param_name} ({expected_importance}): {'记录' if logged else '跳过'}")


def test_parameter_change_logging():
    """测试参数修改日志记录"""
    print("=" * 60)
    print("测试参数修改日志记录")
    print("=" * 60)

    param_logger = get_parameter_logger()

    # 模拟参数修改
    test_changes = [
        {
            "param_name": "max_rpm",
            "old_value": 1500.0,
            "new_value": 1800.0,
            "source": "web_api(*************)",
            "client_info": {
                "device_id": "测试车001",
                "client_ip": "*************"
            }
        },
        {
            "param_name": "spd_kp",
            "old_value": 0.5,
            "new_value": 0.8,
            "source": "auto_setup(*************)",
            "client_info": {
                "device_id": "测试车001",
                "client_ip": "*************"
            }
        },
        {
            "param_name": "sleepTm",
            "old_value": 5.0,
            "new_value": 10.0,
            "source": "test_script",
            "client_info": None
        }
    ]

    for change in test_changes:
        success = param_logger.log_parameter_change(**change)
        print(f"记录参数修改 {change['param_name']}: {'成功' if success else '失败'}")

    print()


def test_client_login_logging():
    """测试客户端登录日志记录"""
    print("=" * 60)
    print("测试客户端登录日志记录")
    print("=" * 60)

    param_logger = get_parameter_logger()

    # 模拟客户端登录
    login_cases = [
        {
            "device_id": "测试车001",
            "client_ip": "*************",
            "web_port": 7002,
            "server_type": "custom"
        },
        {
            "device_id": "测试车002",
            "client_ip": "*************",
            "web_port": 7002,
            "server_type": "default"
        }
    ]

    for login in login_cases:
        param_logger.log_client_login(**login)
        print(f"记录客户端登录: {login['device_id']} from {login['client_ip']}")

    print()


def test_history_queries():
    """测试历史查询功能"""
    print("=" * 60)
    print("测试历史查询功能")
    print("=" * 60)

    param_logger = get_parameter_logger()

    # 查询参数修改历史
    print("查询所有参数修改历史:")
    history = param_logger.get_parameter_history(limit=10)
    for record in history:
        print(f"  {record['timestamp']}: {record['parameter_name']} "
              f"{record['old_value']} -> {record['new_value']} "
              f"({record['source']}) [{record['importance']}]")

    print(f"\n总共找到 {len(history)} 条记录")

    # 查询特定参数的历史
    print("\n查询 max_rpm 参数历史:")
    rpm_history = param_logger.get_parameter_history(param_name="max_rpm", limit=5)
    for record in rpm_history:
        print(f"  {record['timestamp']}: {record['old_value']} -> {record['new_value']} "
              f"({record['source']})")

    # 查询登录历史
    print("\n查询客户端登录历史:")
    login_history = param_logger.get_login_history(limit=5)
    for record in login_history:
        print(f"  {record['timestamp']}: {record['device_id']} from {record['client_ip']} "
              f"port {record['web_port']} ({record['server_type']})")

    print()


def test_statistics():
    """测试统计功能"""
    print("=" * 60)
    print("测试统计功能")
    print("=" * 60)

    param_logger = get_parameter_logger()
    stats = param_logger.get_parameter_stats()

    print("参数统计信息:")
    print(f"  总参数数量: {stats.get('total_parameters', 0)}")

    print("  按重要性分类:")
    for importance, count in stats.get('by_importance', {}).items():
        print(f"    {importance}: {count}")

    if 'recent_changes' in stats:
        recent = stats['recent_changes']
        print(f"\n最近7天修改统计:")
        print(f"  总修改次数: {recent.get('total_changes', 0)}")
        print(f"  涉及参数数: {recent.get('unique_parameters', 0)}")
        print(f"  涉及设备数: {recent.get('unique_devices', 0)}")

    if 'recent_changes_by_importance' in stats:
        print("  按重要性分组的修改次数:")
        for importance, count in stats['recent_changes_by_importance'].items():
            print(f"    {importance}: {count}")

    print()


def main():
    """主测试函数"""
    print("开始测试参数日志功能...")
    print(f"测试时间: {datetime.now().isoformat()}")
    print()

    # 创建临时测试目录
    import tempfile
    temp_dir = tempfile.mkdtemp(prefix="param_log_test_")
    print(f"使用临时目录: {temp_dir}")

    # 设置测试环境
    setup_parameter_logging(
        log_file=os.path.join(temp_dir, "test_parameter_changes.log"),
        min_importance=ParameterImportance.DEBUG
    )

    try:
        # 运行各项测试
        test_parameter_classification()
        test_importance_filtering()
        test_parameter_change_logging()
        test_client_login_logging()

        # 等待一下确保数据写入
        time.sleep(1)

        test_history_queries()
        test_statistics()

        print("=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        print("请检查以下文件:")
        print(f"- {temp_dir}/test_parameter_changes.log (参数修改日志)")
        print(f"- {temp_dir}/parameter_history.db (历史数据库)")
        print(f"- {temp_dir}/test_*.log (不同重要性级别的测试日志)")

        # 清理临时文件
        import shutil
        try:
            shutil.rmtree(temp_dir)
            print(f"\n临时目录已清理: {temp_dir}")
        except Exception as cleanup_error:
            print(f"\n清理临时目录失败: {cleanup_error}")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
